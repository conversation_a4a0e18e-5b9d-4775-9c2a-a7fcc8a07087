import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/facade/health_data_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';

part 'period_tracking_watcher_event.dart';
part 'period_tracking_watcher_state.dart';
part 'period_tracking_watcher_bloc.freezed.dart';

@injectable
class PeriodTrackingWatcherBloc
    extends Bloc<PeriodTrackingWatcherEvent, PeriodTrackingWatcherState> {
  final PeriodTrackingFacade _periodTrackingFacade;
  final HealthDataFacade _healthDataFacade;
  StreamSubscription<
          Either<PeriodTrackingFailure,
              Map<String, Map<String, PeriodTrackingModel>>>>?
      _periodStreamSubscription;

  PeriodTrackingWatcherBloc(this._periodTrackingFacade, this._healthDataFacade)
      : super(const PeriodTrackingWatcherState.initial()) {
    on<_WatchYearStarted>(_onWatchYearStarted);
    on<_PeriodDataReceived>(_onPeriodDataReceived);
  }

  Future<void> _onWatchYearStarted(
    _WatchYearStarted event,
    Emitter<PeriodTrackingWatcherState> emit,
  ) async {
    emit(const PeriodTrackingWatcherState.loading());
    await _periodStreamSubscription?.cancel();

    _periodStreamSubscription =
        _periodTrackingFacade.watchYearData(event.year).listen(
      (failureOrData) {
        if (isClosed) return;
        add(PeriodTrackingWatcherEvent.periodDataReceived(failureOrData));
      },
    );
  }

  Future<void> _onPeriodDataReceived(
    _PeriodDataReceived event,
    Emitter<PeriodTrackingWatcherState> emit,
  ) async {
    event.failureOrData.mapBoth(
      onLeft: (failure) =>
          emit(PeriodTrackingWatcherState.loadFailure(failure)),
      onRight: (data) async {
        // Calculate future predictions
        final futurePredictions = await _calculateFuturePredictions(data);
        emit(PeriodTrackingWatcherState.loadSuccess(data,
            futurePredictions: futurePredictions));
      },
    );
  }

  /// Calculate future period and ovulation predictions until end of year
  Future<Map<String, Set<DateTime>>> _calculateFuturePredictions(
      Map<String, Map<String, PeriodTrackingModel>> yearData) async {
    try {
      // Get user's health data for cycle and period length
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      int cycleLength = 28; // Default cycle length
      int periodLength = 5; // Default period length

      healthDataResult.mapBoth(
        onLeft: (failure) {
          // Use defaults if health data can't be retrieved
        },
        onRight: (healthData) {
          if (healthData != null) {
            cycleLength = healthData.cycleLength ?? 28;
            periodLength = healthData.periodLength ?? 5;
          }
        },
      );

      // Extract existing period dates from year data
      final existingPeriodDates = <DateTime>{};
      for (final monthEntry in yearData.entries) {
        for (final dayEntry in monthEntry.value.entries) {
          final periodModel = dayEntry.value;
          if (periodModel.isPeriodDate == true && periodModel.date != null) {
            existingPeriodDates.add(periodModel.date!);
          }
        }
      }

      final now = DateTime.now();
      final endOfYear = DateTime(now.year, 12, 31);
      final futurePeriodDates = <DateTime>{};
      final futureOvulationDates = <DateTime>{};

      // Find the most recent period date or use tomorrow if no periods exist
      DateTime? lastPeriodDate;
      if (existingPeriodDates.isNotEmpty) {
        final sortedDates = existingPeriodDates.toList()..sort();
        // Find the most recent period start (first day of a period cycle)
        lastPeriodDate = _findMostRecentPeriodStart(sortedDates);
      }

      // If no recent period found or it's more than a month ago, use tomorrow
      if (lastPeriodDate == null ||
          now.difference(lastPeriodDate).inDays > 35) {
        lastPeriodDate = now.add(const Duration(days: 1));
      }

      // Calculate future periods until end of year
      DateTime nextPeriodStart =
          lastPeriodDate.add(Duration(days: cycleLength));

      while (nextPeriodStart.isBefore(endOfYear) ||
          nextPeriodStart.isAtSameMomentAs(endOfYear)) {
        // Only add future dates (after today)
        if (nextPeriodStart.isAfter(now)) {
          // Add period dates
          for (int i = 0; i < periodLength; i++) {
            final periodDate = nextPeriodStart.add(Duration(days: i));
            if (periodDate.isBefore(endOfYear) ||
                periodDate.isAtSameMomentAs(endOfYear)) {
              futurePeriodDates.add(periodDate);
            }
          }

          // Add ovulation date (approximately 14 days before next cycle)
          final ovulationDate =
              nextPeriodStart.add(Duration(days: cycleLength - 14));
          if (ovulationDate.isAfter(now) &&
              (ovulationDate.isBefore(endOfYear) ||
                  ovulationDate.isAtSameMomentAs(endOfYear))) {
            futureOvulationDates.add(ovulationDate);
          }
        }

        nextPeriodStart = nextPeriodStart.add(Duration(days: cycleLength));
      }

      return {
        'periods': futurePeriodDates,
        'ovulations': futureOvulationDates,
      };
    } catch (e) {
      // Return empty predictions if calculation fails
      return {
        'periods': <DateTime>{},
        'ovulations': <DateTime>{},
      };
    }
  }

  /// Find the most recent period start date from existing period dates
  DateTime? _findMostRecentPeriodStart(List<DateTime> sortedPeriodDates) {
    if (sortedPeriodDates.isEmpty) return null;

    final now = DateTime.now();

    // Group consecutive dates into cycles
    final cycles = <List<DateTime>>[];
    List<DateTime> currentCycle = [sortedPeriodDates.first];

    for (int i = 1; i < sortedPeriodDates.length; i++) {
      final currentDate = sortedPeriodDates[i];
      final previousDate = sortedPeriodDates[i - 1];

      // If dates are consecutive (within 2 days), add to current cycle
      if (currentDate.difference(previousDate).inDays <= 2) {
        currentCycle.add(currentDate);
      } else {
        // Start a new cycle
        cycles.add(currentCycle);
        currentCycle = [currentDate];
      }
    }
    cycles.add(currentCycle);

    // Find the most recent cycle that's not too far in the future
    for (final cycle in cycles.reversed) {
      final cycleStart = cycle.first;
      if (cycleStart.isBefore(now) || cycleStart.isAtSameMomentAs(now)) {
        return cycleStart;
      }
    }

    return null;
  }

  @override
  Future<void> close() async {
    await _periodStreamSubscription?.cancel();
    return super.close();
  }
}
